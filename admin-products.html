<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Management - VAITH Admin</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/admin.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar" id="adminSidebar">
            <div class="sidebar-header">
                <a href="admin-dashboard.html" class="sidebar-logo">VAITH</a>
            </div>
            <nav class="sidebar-nav">
                <div class="nav-item">
                    <a href="admin-dashboard.html" class="nav-link">
                        <i class="nav-icon fas fa-chart-line"></i>
                        <span class="nav-text">Dashboard</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="admin-users.html" class="nav-link">
                        <i class="nav-icon fas fa-users"></i>
                        <span class="nav-text">Users</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="admin-products.html" class="nav-link active">
                        <i class="nav-icon fas fa-box"></i>
                        <span class="nav-text">Products</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="admin-orders.html" class="nav-link">
                        <i class="nav-icon fas fa-shopping-cart"></i>
                        <span class="nav-text">Orders</span>
                    </a>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-main" id="adminMain">
            <!-- Header -->
            <header class="admin-header">
                <div class="admin-header-left">
                    <button class="sidebar-toggle" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="page-title">Product Management</h1>
                </div>
                <div class="admin-header-right">
                    <button class="nav-icon theme-toggle" id="themeToggle" title="Toggle dark mode">
                        <i class="fas fa-moon" id="themeIcon"></i>
                    </button>
                    <div class="user-menu">
                        <button class="nav-icon" id="userMenuBtn">
                            <i class="fas fa-user-circle"></i>
                        </button>
                        <div class="dropdown-menu" id="userDropdown">
                            <a href="user-profile.html" class="dropdown-item">
                                <i class="fas fa-user"></i> Profile
                            </a>
                            <a href="user-settings.html" class="dropdown-item">
                                <i class="fas fa-cog"></i> Settings
                            </a>
                            <hr class="dropdown-divider">
                            <a href="#" class="dropdown-item" id="logoutBtn">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <div class="admin-content">
                <!-- Page Header -->
                <div class="page-header">
                    <h2 class="page-title">Products</h2>
                    <p class="page-subtitle">Manage your product catalog and inventory</p>
                </div>

                <!-- Product Stats -->
                <div class="dashboard-grid" style="grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); margin-bottom: 2rem;">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon products">
                                <i class="fas fa-box"></i>
                            </div>
                            <div class="card-content">
                                <h3 id="totalProductsCount">0</h3>
                                <p>Total Products</p>
                            </div>
                        </div>
                    </div>
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="card-content">
                                <h3 id="activeProductsCount">0</h3>
                                <p>Active Products</p>
                            </div>
                        </div>
                    </div>
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="card-content">
                                <h3 id="outOfStockCount">0</h3>
                                <p>Out of Stock</p>
                            </div>
                        </div>
                    </div>
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <div class="card-content">
                                <h3 id="lowStockCount">0</h3>
                                <p>Low Stock</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters and Search -->
                <div class="data-table-container">
                    <div class="table-header">
                        <h3 class="table-title">All Products</h3>
                        <div class="table-actions">
                            <div class="search-box" style="margin-right: 1rem;">
                                <input type="text" id="productSearch" placeholder="Search products..." style="padding: 0.5rem; border: 1px solid var(--border-color); border-radius: 0.25rem; background: var(--input-bg); color: var(--text-color);">
                            </div>
                            <select id="categoryFilter" class="form-select" style="margin-right: 1rem; padding: 0.5rem; min-width: 120px;">
                                <option value="">All Categories</option>
                                <option value="men">Men</option>
                                <option value="women">Women</option>
                            </select>
                            <select id="statusFilter" class="form-select" style="margin-right: 1rem; padding: 0.5rem; min-width: 120px;">
                                <option value="">All Status</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                                <option value="draft">Draft</option>
                            </select>
                            <button class="btn btn-primary btn-sm" id="addProductBtn" onclick="openAddProductModal()" title="Add New Product">
                                <i class="fas fa-plus"></i> Add Product
                            </button>
                        </div>
                    </div>

                    <!-- Products Table -->
                    <div class="table-wrapper">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th style="min-width: 200px;">Product</th>
                                    <th style="min-width: 100px;">SKU</th>
                                    <th style="min-width: 120px;">Category</th>
                                    <th style="min-width: 100px;">Price</th>
                                    <th style="min-width: 80px;">Stock</th>
                                    <th style="min-width: 100px;">Status</th>
                                    <th style="min-width: 100px;">Rating</th>
                                    <th style="min-width: 150px;">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="productsTableBody">
                                <!-- Products will be loaded dynamically -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Product Details Modal -->
    <div class="modal" id="productModal" style="display: none;">
        <div class="modal-content" style="max-width: 800px;">
            <div class="modal-header">
                <h3 id="modalTitle">Product Details</h3>
                <button class="close-modal" id="closeProductModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="productModalBody">
                <!-- Product details will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Overlay -->
    <div class="overlay" id="overlay" style="display: none;"></div>

    <!-- Modal and Dropdown Styles -->
    <style>
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 2000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: var(--card-bg);
            border-radius: 0.75rem;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
            max-height: 90vh;
            overflow-y: auto;
            width: 90%;
            max-width: 500px;
        }

        .modal-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .modal-header h3 {
            margin: 0;
            color: var(--text-color);
        }

        .close-modal {
            background: none;
            border: none;
            font-size: 1.25rem;
            color: var(--text-light);
            cursor: pointer;
            padding: 0.25rem;
        }

        .modal-body {
            padding: 1.5rem;
        }

        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1999;
        }

        .user-menu {
            position: relative;
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            min-width: 200px;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
        }

        .dropdown-menu.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1rem;
            color: var(--text-color);
            text-decoration: none;
            transition: background-color 0.2s;
        }

        .dropdown-item:hover {
            background: var(--section-bg);
        }

        .dropdown-divider {
            margin: 0.5rem 0;
            border: none;
            border-top: 1px solid var(--border-color);
        }



        .stock-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
        }

        .stock-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .stock-high { background: var(--success-color); }
        .stock-medium { background: var(--warning-color); }
        .stock-low { background: var(--error-color); }
        .stock-out { background: var(--text-light); }
    </style>

    <!-- Scripts -->
    <script src="js/theme-toggle.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/admin.js"></script>

    <script>
        let currentProducts = [];
        let filteredProducts = [];

        document.addEventListener('DOMContentLoaded', function() {
            console.log('Admin products page - DOM loaded');

            // Check admin access
            if (!authManager.requireAdmin()) {
                return;
            }

            // Initialize admin manager if not already done
            if (typeof adminManager === 'undefined' && typeof AdminManager !== 'undefined') {
                window.adminManager = new AdminManager();
                console.log('Admin manager initialized');
            }

            // Initialize page
            initializeProductsPage();
            setupEventListeners();

            console.log('Admin products page initialization complete');
        });

        function initializeProductsPage() {
            loadProductStats();
            loadProducts();
        }

        function loadProductStats() {
            const stats = adminManager.getProductStats();
            document.getElementById('totalProductsCount').textContent = stats.total;
            document.getElementById('activeProductsCount').textContent = stats.active;
            document.getElementById('outOfStockCount').textContent = stats.outOfStock;
            document.getElementById('lowStockCount').textContent = stats.lowStock;
        }

        function loadProducts() {
            console.log('Loading products...');
            if (typeof adminManager === 'undefined') {
                console.error('AdminManager not available');
                return;
            }

            currentProducts = adminManager.getAllProducts();
            filteredProducts = [...currentProducts];
            console.log('Loaded products:', currentProducts.length);
            renderProductsTable();
        }

        function renderProductsTable() {
            const tbody = document.getElementById('productsTableBody');
            
            if (filteredProducts.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 3rem; color: var(--admin-text-tertiary);">
                            <div style="display: flex; flex-direction: column; align-items: center; gap: 1rem;">
                                <i class="fas fa-box-open" style="font-size: 3rem; opacity: 0.5;"></i>
                                <div>
                                    <div style="font-weight: 600; margin-bottom: 0.5rem; font-size: 1.1rem;">No products found</div>
                                    <div style="font-size: 0.875rem;">Start by adding your first product to the store</div>
                                </div>
                                <button class="btn btn-primary" onclick="openAddProductModal()">
                                    <i class="fas fa-plus"></i> Add Your First Product
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = filteredProducts.map(product => `
                <tr>
                    <td>
                        <div>
                            <div style="font-weight: 500; margin-bottom: 0.25rem;">${product.name}</div>
                            <div style="font-size: 0.75rem; color: var(--text-light);">${product.brand}</div>
                        </div>
                    </td>
                    <td style="font-family: monospace;">${product.sku}</td>
                    <td><span class="status-badge status-active">${product.category}</span></td>
                    <td>
                        <div style="font-weight: 500;">$${product.price}</div>
                        ${product.originalPrice && product.originalPrice !== product.price ? 
                            `<div style="font-size: 0.75rem; color: var(--text-light); text-decoration: line-through;">$${product.originalPrice}</div>` : 
                            ''
                        }
                    </td>
                    <td>
                        <div class="stock-indicator">
                            <div class="stock-dot ${getStockIndicatorClass(product.stock)}"></div>
                            <span>${product.stock}</span>
                        </div>
                    </td>
                    <td><span class="status-badge ${getProductStatusBadgeClass(product.status)}">${product.status}</span></td>
                    <td>
                        <div style="display: flex; align-items: center; gap: 0.25rem;">
                            <span style="color: #fbbf24;">★</span>
                            <span>${product.rating.toFixed(1)}</span>
                            <span style="color: var(--text-light); font-size: 0.75rem;">(${product.reviews})</span>
                        </div>
                    </td>
                    <td>
                        <div style="display: flex; gap: 0.5rem;">
                            <button class="btn btn-secondary btn-sm" onclick="viewProduct(${product.id})" title="View Product Details">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-secondary btn-sm" onclick="editProduct(${product.id})" title="Edit Product">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="deleteProduct(${product.id})" title="Delete Product">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        function setupEventListeners() {
            // Sidebar toggle
            document.getElementById('sidebarToggle').addEventListener('click', function() {
                const sidebar = document.getElementById('adminSidebar');
                const main = document.getElementById('adminMain');
                
                sidebar.classList.toggle('collapsed');
                main.classList.toggle('expanded');
            });

            // Search functionality
            document.getElementById('productSearch').addEventListener('input', applyFilters);

            // Filter functionality
            document.getElementById('categoryFilter').addEventListener('change', applyFilters);
            document.getElementById('statusFilter').addEventListener('change', applyFilters);

            // User menu toggle
            document.getElementById('userMenuBtn').addEventListener('click', function(e) {
                e.stopPropagation();
                const dropdown = document.getElementById('userDropdown');
                dropdown.classList.toggle('show');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function() {
                const dropdown = document.getElementById('userDropdown');
                dropdown.classList.remove('show');
            });

            // Modal close
            document.getElementById('closeProductModal').addEventListener('click', closeModal);

            // Logout
            document.getElementById('logoutBtn').addEventListener('click', function(e) {
                e.preventDefault();
                authManager.logout();
            });
        }

        function applyFilters() {
            const searchQuery = document.getElementById('productSearch').value;
            const categoryFilter = document.getElementById('categoryFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;

            filteredProducts = currentProducts.filter(product => {
                const matchesSearch = !searchQuery || 
                    product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    product.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    product.sku.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    product.brand.toLowerCase().includes(searchQuery.toLowerCase());

                const matchesCategory = !categoryFilter || product.category === categoryFilter;
                const matchesStatus = !statusFilter || product.status === statusFilter;

                return matchesSearch && matchesCategory && matchesStatus;
            });

            renderProductsTable();
        }

        function getStockIndicatorClass(stock) {
            if (stock === 0) return 'stock-out';
            if (stock <= 5) return 'stock-low';
            if (stock <= 20) return 'stock-medium';
            return 'stock-high';
        }

        function viewProduct(productId) {
            const product = adminManager.getProductById(productId);
            if (!product) return;

            document.getElementById('modalTitle').textContent = 'Product Details';
            document.getElementById('productModalBody').innerHTML = `
                <div style="display: grid; gap: 1.5rem;">
                    <div>
                        <h3 style="margin-bottom: 0.5rem;">${product.name}</h3>
                        <p style="color: var(--text-light); margin-bottom: 1rem;">${product.description}</p>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                            <div><strong>Brand:</strong> ${product.brand}</div>
                            <div><strong>SKU:</strong> ${product.sku}</div>
                            <div><strong>Category:</strong> ${product.category}</div>
                            <div><strong>Status:</strong> <span class="status-badge ${getProductStatusBadgeClass(product.status)}">${product.status}</span></div>
                        </div>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 1rem;">
                        <div>
                            <strong>Price:</strong><br>
                            <span style="font-size: 1.25rem; font-weight: 600; color: var(--primary-color);">$${product.price}</span>
                            ${product.originalPrice && product.originalPrice !== product.price ? 
                                `<span style="text-decoration: line-through; color: var(--text-light); margin-left: 0.5rem;">$${product.originalPrice}</span>` : 
                                ''
                            }
                        </div>
                        <div>
                            <strong>Stock:</strong><br>
                            <div class="stock-indicator" style="margin-top: 0.25rem;">
                                <div class="stock-dot ${getStockIndicatorClass(product.stock)}"></div>
                                <span>${product.stock} units</span>
                            </div>
                        </div>
                        <div>
                            <strong>Rating:</strong><br>
                            <div style="display: flex; align-items: center; gap: 0.25rem; margin-top: 0.25rem;">
                                <span style="color: #fbbf24;">★</span>
                                <span>${product.rating.toFixed(1)}</span>
                                <span style="color: var(--text-light);">(${product.reviews} reviews)</span>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <strong>Available Sizes:</strong><br>
                        <div style="display: flex; gap: 0.5rem; margin-top: 0.5rem;">
                            ${product.sizes.map(size => `<span class="status-badge status-active">${size}</span>`).join('')}
                        </div>
                    </div>
                    
                    <div>
                        <strong>Available Colors:</strong><br>
                        <div style="display: flex; gap: 0.5rem; margin-top: 0.5rem;">
                            ${product.colors.map(color => `<span class="status-badge status-pending">${color}</span>`).join('')}
                        </div>
                    </div>
                    
                    <div>
                        <strong>Created:</strong> ${formatDate(product.createdDate)}<br>
                        <strong>Last Updated:</strong> ${formatDate(product.updatedDate)}
                    </div>
                </div>
            `;
            
            showModal();
        }

        function editProduct(productId) {
            // For now, just show view - in a real app, this would open an edit form
            viewProduct(productId);
        }

        function deleteProduct(productId) {
            const product = adminManager.getProductById(productId);
            if (!product) return;

            if (confirm(`Are you sure you want to delete "${product.name}"? This action cannot be undone.`)) {
                adminManager.deleteProduct(productId);
                loadProducts();
                loadProductStats();
            }
        }

        function showModal() {
            document.getElementById('productModal').style.display = 'flex';
            document.getElementById('overlay').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('productModal').style.display = 'none';
            document.getElementById('overlay').style.display = 'none';
        }

        // Add Product Modal Functions
        function openAddProductModal() {
            console.log('Opening add product modal...');
            const modal = document.getElementById('addProductModal');
            const overlay = document.getElementById('overlay');

            if (modal && overlay) {
                modal.style.display = 'flex';
                overlay.style.display = 'block';
                // Generate SKU
                generateSKU();
                console.log('Add product modal opened successfully');
            } else {
                console.error('Modal or overlay element not found');
            }
        }

        function closeAddProductModal() {
            document.getElementById('addProductModal').style.display = 'none';
            document.getElementById('overlay').style.display = 'none';
            document.getElementById('addProductForm').reset();
        }

        function generateSKU() {
            const timestamp = Date.now().toString().slice(-6);
            const random = Math.random().toString(36).substr(2, 3).toUpperCase();
            const sku = `VTH-${random}-${timestamp}`;
            const skuInput = document.getElementById('productSku');

            if (skuInput) {
                skuInput.value = sku;
                console.log('Generated SKU:', sku);
            } else {
                console.error('SKU input field not found');
            }
        }

        // Handle Add Product Form Submission
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('addProductForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const productData = {};

            // Get form data
            for (let [key, value] = formData.entries()) {
                productData[key] = value;
            }

            // Process images
            const imageUrls = productData.images ? productData.images.split('\n').filter(url => url.trim()) : [];
            productData.images = imageUrls;

            // Process sizes and colors
            productData.sizes = productData.sizes ? productData.sizes.split(',').map(s => s.trim()).filter(s => s) : [];
            productData.colors = productData.colors ? productData.colors.split(',').map(c => c.trim()).filter(c => c) : [];

            // Convert numeric fields
            productData.price = parseFloat(productData.price);
            productData.originalPrice = productData.originalPrice ? parseFloat(productData.originalPrice) : null;
            productData.stock = parseInt(productData.stock);

            // Generate SKU if empty
            if (!productData.sku) {
                generateSKU();
                productData.sku = document.getElementById('productSku').value;
            }

            // Add additional fields
            productData.id = Date.now();
            productData.rating = 0;
            productData.reviews = 0;
            productData.createdDate = new Date().toISOString();
            productData.updatedDate = new Date().toISOString();

            // Validate required fields
            if (!productData.name || !productData.category || !productData.price || productData.stock < 0) {
                adminInterface.showToast('Please fill in all required fields correctly', 'error');
                return;
            }

            try {
                // Check if adminManager is available
                if (typeof adminManager === 'undefined') {
                    throw new Error('Admin manager not initialized');
                }

                // Add product using admin manager
                console.log('Adding product:', productData);
                adminManager.addProduct(productData);

                // Show success message
                adminInterface.showToast(`Product "${productData.name}" added successfully!`, 'success');

                // Close modal and refresh table
                closeAddProductModal();
                loadProducts();

                // Update dashboard stats if on dashboard
                if (typeof loadDashboardStats === 'function') {
                    loadDashboardStats();
                }

            } catch (error) {
                console.error('Error adding product:', error);
                adminInterface.showToast('Error adding product. Please try again.', 'error');
            }
            });
        });

        // Close modals when clicking overlay
        document.getElementById('overlay').addEventListener('click', function() {
            // Check which modal is open and close the appropriate one
            const addProductModal = document.getElementById('addProductModal');
            const productModal = document.getElementById('productModal');

            if (addProductModal.style.display === 'flex') {
                closeAddProductModal();
            } else if (productModal.style.display === 'flex') {
                closeModal();
            }
        });
    </script>

    <!-- Add Product Modal -->
    <div class="modal" id="addProductModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; align-items: center; justify-content: center;">
        <div class="modal-content" style="max-width: 800px; max-height: 90vh; overflow-y: auto; background: var(--admin-card-bg); border-radius: 12px; box-shadow: var(--admin-shadow-2xl);">
            <div class="modal-header">
                <h3><i class="fas fa-plus"></i> Add New Product</h3>
                <button class="close-modal" id="closeAddProductModal" onclick="closeAddProductModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="addProductForm">
                    <div class="form-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <!-- Basic Information -->
                        <div class="form-group" style="grid-column: 1 / -1;">
                            <h4 style="margin-bottom: 1rem; color: var(--admin-text-primary); border-bottom: 1px solid var(--admin-border); padding-bottom: 0.5rem;">
                                <i class="fas fa-info-circle"></i> Basic Information
                            </h4>
                        </div>

                        <div class="form-group">
                            <label for="productName">Product Name *</label>
                            <input type="text" id="productName" name="name" required placeholder="Enter product name">
                        </div>

                        <div class="form-group">
                            <label for="productBrand">Brand</label>
                            <input type="text" id="productBrand" name="brand" placeholder="Enter brand name" value="VAITH">
                        </div>

                        <div class="form-group">
                            <label for="productCategory">Category *</label>
                            <select id="productCategory" name="category" required>
                                <option value="">Select Category</option>
                                <option value="women">Women</option>
                                <option value="men">Men</option>
                                <option value="accessories">Accessories</option>
                                <option value="shoes">Shoes</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="productSubcategory">Subcategory</label>
                            <input type="text" id="productSubcategory" name="subcategory" placeholder="e.g., tops, bottoms, dresses">
                        </div>

                        <div class="form-group" style="grid-column: 1 / -1;">
                            <label for="productDescription">Description</label>
                            <textarea id="productDescription" name="description" rows="3" placeholder="Enter product description"></textarea>
                        </div>

                        <!-- Pricing -->
                        <div class="form-group" style="grid-column: 1 / -1;">
                            <h4 style="margin: 1.5rem 0 1rem 0; color: var(--admin-text-primary); border-bottom: 1px solid var(--admin-border); padding-bottom: 0.5rem;">
                                <i class="fas fa-dollar-sign"></i> Pricing & Inventory
                            </h4>
                        </div>

                        <div class="form-group">
                            <label for="productPrice">Price *</label>
                            <input type="number" id="productPrice" name="price" step="0.01" min="0" required placeholder="0.00">
                        </div>

                        <div class="form-group">
                            <label for="productOriginalPrice">Original Price</label>
                            <input type="number" id="productOriginalPrice" name="originalPrice" step="0.01" min="0" placeholder="0.00">
                        </div>

                        <div class="form-group">
                            <label for="productStock">Stock Quantity *</label>
                            <input type="number" id="productStock" name="stock" min="0" required placeholder="0">
                        </div>

                        <div class="form-group">
                            <label for="productSku">SKU</label>
                            <input type="text" id="productSku" name="sku" placeholder="Auto-generated if empty">
                        </div>

                        <!-- Images -->
                        <div class="form-group" style="grid-column: 1 / -1;">
                            <h4 style="margin: 1.5rem 0 1rem 0; color: var(--admin-text-primary); border-bottom: 1px solid var(--admin-border); padding-bottom: 0.5rem;">
                                <i class="fas fa-images"></i> Product Images
                            </h4>
                        </div>

                        <div class="form-group" style="grid-column: 1 / -1;">
                            <label for="productImages">Image URLs (one per line)</label>
                            <textarea id="productImages" name="images" rows="4" placeholder="https://example.com/image1.jpg&#10;https://example.com/image2.jpg"></textarea>
                            <small style="color: var(--admin-text-tertiary); font-size: 0.875rem;">
                                Enter image URLs, one per line. First image will be the main product image.
                            </small>
                        </div>

                        <!-- Additional Options -->
                        <div class="form-group" style="grid-column: 1 / -1;">
                            <h4 style="margin: 1.5rem 0 1rem 0; color: var(--admin-text-primary); border-bottom: 1px solid var(--admin-border); padding-bottom: 0.5rem;">
                                <i class="fas fa-cog"></i> Additional Options
                            </h4>
                        </div>

                        <div class="form-group">
                            <label for="productSizes">Available Sizes</label>
                            <input type="text" id="productSizes" name="sizes" placeholder="S, M, L, XL">
                            <small style="color: var(--admin-text-tertiary); font-size: 0.875rem;">
                                Separate sizes with commas
                            </small>
                        </div>

                        <div class="form-group">
                            <label for="productColors">Available Colors</label>
                            <input type="text" id="productColors" name="colors" placeholder="Black, White, Navy">
                            <small style="color: var(--admin-text-tertiary); font-size: 0.875rem;">
                                Separate colors with commas
                            </small>
                        </div>

                        <div class="form-group">
                            <label for="productStatus">Status *</label>
                            <select id="productStatus" name="status" required>
                                <option value="active">Active</option>
                                <option value="draft">Draft</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeAddProductModal()">Cancel</button>
                <button type="submit" form="addProductForm" class="btn btn-primary">
                    <i class="fas fa-save"></i> Add Product
                </button>
            </div>
        </div>
    </div>
</body>
</html>
