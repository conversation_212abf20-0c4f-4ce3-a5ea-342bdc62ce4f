<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dark Mode Size Options Test - VAITH</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            padding: 2rem;
            min-height: 100vh;
            background: var(--background-color);
            color: var(--text-color);
            transition: all 0.3s ease;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: var(--card-bg);
            padding: 2rem;
            border-radius: 12px;
            border: 1px solid var(--border-color);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .test-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .test-header h1 {
            color: var(--text-color);
            margin-bottom: 0.5rem;
        }

        .test-header p {
            color: var(--text-light);
        }

        .theme-toggle-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin-bottom: 2rem;
            transition: all 0.3s ease;
        }

        .theme-toggle-btn:hover {
            background: #6a1b9a;
            transform: translateY(-2px);
        }

        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--section-bg);
        }

        .test-section h3 {
            color: var(--text-color);
            margin-bottom: 1rem;
        }

        /* Size options styles (copied from product.html) */
        .option-label {
            display: block;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #333;
        }

        [data-theme="dark"] .option-label,
        body.dark-theme .option-label {
            color: var(--text-color);
        }

        .size-options {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .size-option {
            padding: 8px 16px;
            border: 2px solid #ddd;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
            color: #333;
        }

        .size-option:hover {
            border-color: #4B0082;
        }

        .size-option.selected {
            border-color: #4B0082;
            background: #4B0082;
            color: white;
        }

        /* Dark mode styles for size options */
        [data-theme="dark"] .size-option,
        body.dark-theme .size-option {
            background: var(--card-bg);
            border-color: var(--border-color);
            color: var(--text-color);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }

        [data-theme="dark"] .size-option:hover,
        body.dark-theme .size-option:hover {
            border-color: #8b5cf6;
            background: rgba(139, 92, 246, 0.15);
            color: #a78bfa;
            box-shadow: 0 2px 8px rgba(139, 92, 246, 0.2);
        }

        [data-theme="dark"] .size-option.selected,
        body.dark-theme .size-option.selected {
            border-color: #8b5cf6;
            background: linear-gradient(135deg, #8b5cf6, #a78bfa);
            color: white;
            box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
        }

        [data-theme="dark"] .size-option.selected:hover,
        body.dark-theme .size-option.selected:hover {
            background: linear-gradient(135deg, #7c3aed, #8b5cf6);
            box-shadow: 0 6px 16px rgba(139, 92, 246, 0.4);
        }

        .demo-price {
            font-size: 1.5rem;
            font-weight: 700;
            color: #4B0082;
            margin: 1rem 0;
        }

        [data-theme="dark"] .demo-price,
        body.dark-theme .demo-price {
            color: #8b5cf6;
        }

        .status {
            padding: 1rem;
            border-radius: 8px;
            margin-top: 1rem;
            font-weight: 500;
        }

        .status.light {
            background: #e3f2fd;
            color: #1565c0;
            border: 1px solid #bbdefb;
        }

        .status.dark {
            background: rgba(139, 92, 246, 0.1);
            color: #8b5cf6;
            border: 1px solid rgba(139, 92, 246, 0.3);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>Dark Mode Size Options Test</h1>
            <p>Test the size option colors in both light and dark modes</p>
            <button class="theme-toggle-btn" onclick="toggleTheme()">
                <i class="fas fa-moon" id="themeIcon"></i>
                Toggle Dark Mode
            </button>
        </div>

        <div class="test-section">
            <h3>Size Selection Demo</h3>
            <label class="option-label">Choose Size:</label>
            <div class="size-options">
                <div class="size-option" onclick="selectSize(this)">XS</div>
                <div class="size-option" onclick="selectSize(this)">S</div>
                <div class="size-option selected" onclick="selectSize(this)">M</div>
                <div class="size-option" onclick="selectSize(this)">L</div>
                <div class="size-option" onclick="selectSize(this)">XL</div>
                <div class="size-option" onclick="selectSize(this)">XXL</div>
            </div>
            <div class="demo-price">$29.99</div>
        </div>

        <div class="test-section">
            <h3>Theme Status</h3>
            <div class="status" id="themeStatus">
                Current theme: Light Mode
            </div>
        </div>
    </div>

    <script src="js/theme-toggle.js"></script>
    <script>
        function selectSize(element) {
            // Remove selected class from all size options
            document.querySelectorAll('.size-option').forEach(el => el.classList.remove('selected'));
            // Add selected class to clicked element
            element.classList.add('selected');
        }

        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            
            // Apply theme using the theme-toggle.js function
            if (window.applyTheme) {
                window.applyTheme(newTheme);
            } else {
                // Fallback if theme-toggle.js is not loaded
                document.documentElement.setAttribute('data-theme', newTheme);
                if (newTheme === 'dark') {
                    document.body.classList.add('dark-theme');
                } else {
                    document.body.classList.remove('dark-theme');
                }
            }
            
            localStorage.setItem('theme', newTheme);
            updateThemeStatus(newTheme);
            updateThemeIcon(newTheme);
        }

        function updateThemeStatus(theme) {
            const statusElement = document.getElementById('themeStatus');
            statusElement.textContent = `Current theme: ${theme === 'dark' ? 'Dark' : 'Light'} Mode`;
            statusElement.className = `status ${theme}`;
        }

        function updateThemeIcon(theme) {
            const icon = document.getElementById('themeIcon');
            if (theme === 'dark') {
                icon.className = 'fas fa-sun';
            } else {
                icon.className = 'fas fa-moon';
            }
        }

        // Initialize theme on page load
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            if (savedTheme === 'dark') {
                toggleTheme();
            }
            updateThemeStatus(savedTheme);
            updateThemeIcon(savedTheme);
        });
    </script>
</body>
</html>
