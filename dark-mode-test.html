<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dark Mode Test - VAITH</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .test-section {
            padding: 2rem;
            margin: 1rem 0;
            border: 1px solid var(--border-color);
            border-radius: 10px;
            background: var(--card-bg);
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .test-card {
            padding: 1rem;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            color: var(--text-color);
        }
        
        .status-indicator {
            position: fixed;
            top: 100px;
            right: 20px;
            padding: 1rem;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-family: monospace;
            z-index: 1000;
        }
        
        .theme-info {
            margin: 0.5rem 0;
            font-size: 0.9rem;
        }
        
        .test-button {
            margin: 0.5rem;
            padding: 0.5rem 1rem;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .test-input {
            width: 100%;
            padding: 0.5rem;
            margin: 0.5rem 0;
            background: var(--input-bg);
            border: 1px solid var(--input-border);
            border-radius: 5px;
            color: var(--text-color);
        }
        
        .test-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--modal-overlay);
            z-index: 2000;
        }
        
        .test-modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: var(--modal-bg);
            padding: 2rem;
            border-radius: 10px;
            border: 1px solid var(--border-color);
            color: var(--text-color);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-left">
                <div class="logo">
                    <a href="index.html">
                        <svg width="90" height="40" viewBox="0 0 90 40" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#4B0082;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#D8BFD8;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                            <text x="5" y="28" font-family="Inter, sans-serif" font-size="24" font-weight="700" fill="url(#logoGradient)">VAITH</text>
                        </svg>
                    </a>
                </div>
            </div>
            
            <div class="nav-center">
                <div class="search-box">
                    <input type="text" placeholder="Search for products..." id="searchInput">
                    <button class="search-btn"><i class="fas fa-search"></i></button>
                </div>
            </div>

            <div class="nav-right">
                <div class="nav-icons">
                    <button class="nav-icon theme-toggle" id="themeToggle" title="Toggle dark mode" aria-label="Toggle dark mode">
                        <i class="fas fa-moon" id="themeIcon" aria-hidden="true"></i>
                    </button>
                    <button class="nav-icon">
                        <i class="fas fa-user"></i>
                    </button>
                    <button class="nav-icon">
                        <i class="fas fa-heart"></i>
                        <span class="badge">3</span>
                    </button>
                    <button class="nav-icon" id="testModalBtn">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="badge">2</span>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Status Indicator -->
    <div class="status-indicator">
        <div class="theme-info">Theme: <span id="currentTheme">light</span></div>
        <div class="theme-info">Data Attribute: <span id="dataTheme">none</span></div>
        <div class="theme-info">Body Class: <span id="bodyClass">none</span></div>
        <button class="test-button" onclick="runThemeTest()">Test Toggle</button>
    </div>

    <!-- Main Content -->
    <div class="container" style="margin-top: 100px;">
        <h1 class="section-title">Dark Mode Comprehensive Test</h1>
        
        <!-- Component Tests -->
        <div class="test-section">
            <h2>Navigation & Search</h2>
            <p>Test the navigation bar, search box, and theme toggle button above.</p>
        </div>

        <div class="test-section">
            <h2>Cards & Content</h2>
            <div class="test-grid">
                <div class="test-card">
                    <h3>Test Card 1</h3>
                    <p>This card should change colors with the theme.</p>
                    <button class="test-button">Action Button</button>
                </div>
                <div class="test-card">
                    <h3>Test Card 2</h3>
                    <p>Background and text should have proper contrast.</p>
                    <input type="text" class="test-input" placeholder="Test input field">
                </div>
                <div class="test-card">
                    <h3>Test Card 3</h3>
                    <p>Borders and shadows should be visible in both themes.</p>
                    <button class="test-button" onclick="showTestModal()">Open Modal</button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>Form Elements</h2>
            <input type="text" class="test-input" placeholder="Text input">
            <input type="email" class="test-input" placeholder="Email input">
            <textarea class="test-input" placeholder="Textarea" rows="3"></textarea>
            <button class="test-button">Submit Button</button>
        </div>

        <div class="test-section">
            <h2>Product Card Simulation</h2>
            <div class="products-grid">
                <div class="product-card">
                    <div class="product-image">
                        <img src="https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=300&h=300&fit=crop" alt="Test Product">
                        <div class="product-actions">
                            <button class="action-btn"><i class="fas fa-heart"></i></button>
                            <button class="action-btn"><i class="fas fa-eye"></i></button>
                        </div>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">Test Product</h3>
                        <div class="product-price">
                            <span class="current-price">$29.99</span>
                            <span class="original-price">$39.99</span>
                            <span class="discount-badge">25% OFF</span>
                        </div>
                        <div class="product-rating">
                            <div class="stars">★★★★☆</div>
                            <span class="rating-count">(42)</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Modal -->
    <div class="test-modal" id="testModal">
        <div class="test-modal-content">
            <h3>Test Modal</h3>
            <p>This modal should have proper dark mode styling.</p>
            <button class="test-button" onclick="hideTestModal()">Close</button>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/theme-toggle.js"></script>
    <script>
        // Update status display
        function updateStatus() {
            const theme = document.documentElement.getAttribute('data-theme') || 'light';
            const bodyClasses = document.body.className || 'none';
            
            document.getElementById('currentTheme').textContent = theme;
            document.getElementById('dataTheme').textContent = theme;
            document.getElementById('bodyClass').textContent = bodyClasses;
        }

        // Test functions
        function runThemeTest() {
            if (window.testThemeToggle) {
                window.testThemeToggle();
                setTimeout(updateStatus, 100);
            }
        }

        function showTestModal() {
            document.getElementById('testModal').style.display = 'block';
        }

        function hideTestModal() {
            document.getElementById('testModal').style.display = 'none';
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus();
            
            // Watch for theme changes
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && 
                        (mutation.attributeName === 'data-theme' || mutation.attributeName === 'class')) {
                        updateStatus();
                    }
                });
            });

            observer.observe(document.documentElement, {
                attributes: true,
                attributeFilter: ['data-theme']
            });
            
            observer.observe(document.body, {
                attributes: true,
                attributeFilter: ['class']
            });

            // Listen for custom theme change events
            document.addEventListener('themeChanged', function(e) {
                console.log('Theme changed to:', e.detail.theme);
                updateStatus();
            });
        });
    </script>
</body>
</html>
