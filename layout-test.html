<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Layout Test - VAITH</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/admin.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Test styles to highlight layout issues */
        .test-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 10000;
        }
        
        .overflow-test {
            border: 2px dashed red;
            margin: 10px 0;
            padding: 10px;
            background: rgba(255, 0, 0, 0.1);
        }
        
        .width-test {
            background: linear-gradient(90deg, 
                rgba(255,0,0,0.1) 0%, 
                rgba(255,255,0,0.1) 25%, 
                rgba(0,255,0,0.1) 50%, 
                rgba(0,255,255,0.1) 75%, 
                rgba(0,0,255,0.1) 100%);
            height: 20px;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="test-info">
        <div>Screen: <span id="screenSize"></span></div>
        <div>Viewport: <span id="viewportSize"></span></div>
        <div>Overflow: <span id="overflowStatus">Checking...</span></div>
    </div>

    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar" id="adminSidebar">
            <div class="sidebar-header">
                <a href="#" class="sidebar-logo">VAITH TEST</a>
            </div>
            <nav class="sidebar-nav">
                <div class="nav-item">
                    <a href="#" class="nav-link active">
                        <i class="nav-icon fas fa-chart-line"></i>
                        <span class="nav-text">Dashboard</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="nav-icon fas fa-users"></i>
                        <span class="nav-text">Users</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="nav-icon fas fa-box"></i>
                        <span class="nav-text">Products</span>
                    </a>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-main" id="adminMain">
            <!-- Header -->
            <header class="admin-header">
                <div class="admin-header-left">
                    <button class="sidebar-toggle" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="admin-search">
                        <i class="admin-search-icon fas fa-search"></i>
                        <input type="text" class="admin-search-input" placeholder="Search...">
                    </div>
                </div>
                <div class="admin-header-right">
                    <button class="nav-icon">
                        <i class="fas fa-bell"></i>
                    </button>
                    <div class="profile-avatar">AT</div>
                </div>
            </header>

            <!-- Content -->
            <div class="admin-content">
                <div class="page-header">
                    <h2 class="page-title">Layout Width Test</h2>
                    <p class="page-subtitle">Testing responsive layout and preventing horizontal overflow</p>
                </div>

                <!-- Width Test Indicators -->
                <div class="overflow-test">
                    <h4>Width Test Indicators</h4>
                    <div class="width-test"></div>
                    <p>The colored bar above should never extend beyond the container width.</p>
                </div>

                <!-- Dashboard Cards Test -->
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon users">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="card-content">
                                <h3>1,234</h3>
                                <p>Test Card with Long Title That Should Not Overflow</p>
                                <div class="card-trend trend-up">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>+12.5%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon orders">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div class="card-content">
                                <h3>5,678</h3>
                                <p>Another Test Card</p>
                                <div class="card-trend trend-up">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>+8.2%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Table Test -->
                <div class="data-table-container">
                    <div class="table-header">
                        <h3 class="table-title">Responsive Table Test</h3>
                        <div class="table-actions">
                            <button class="btn btn-secondary btn-sm">Action 1</button>
                            <button class="btn btn-secondary btn-sm">Action 2</button>
                            <button class="btn btn-primary btn-sm">Primary Action</button>
                        </div>
                    </div>
                    <div class="table-wrapper">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th style="min-width: 200px;">Very Long Column Header Name</th>
                                    <th style="min-width: 150px;">Another Long Header</th>
                                    <th style="min-width: 120px;">Status</th>
                                    <th style="min-width: 150px;">Date Created</th>
                                    <th style="min-width: 200px;">Actions Column</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Very long content that should not cause horizontal overflow in the main page</td>
                                    <td>More long content to test table behavior</td>
                                    <td><span class="status-badge status-active">Active</span></td>
                                    <td>2024-01-15</td>
                                    <td>
                                        <button class="btn btn-secondary btn-sm">Edit</button>
                                        <button class="btn btn-danger btn-sm">Delete</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Another row with potentially long content</td>
                                    <td>Testing responsive behavior</td>
                                    <td><span class="status-badge status-pending">Pending</span></td>
                                    <td>2024-01-14</td>
                                    <td>
                                        <button class="btn btn-secondary btn-sm">Edit</button>
                                        <button class="btn btn-danger btn-sm">Delete</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Form Test -->
                <div class="data-table-container" style="margin-top: 2rem;">
                    <div class="table-header">
                        <h3 class="table-title">Form Width Test</h3>
                    </div>
                    <div style="padding: 2rem;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                            <div class="form-group">
                                <label class="form-label">Very Long Label Name That Should Not Overflow</label>
                                <input type="text" class="form-input" placeholder="Test input field">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Another Field</label>
                                <select class="form-select">
                                    <option>Very long option text that should not cause issues</option>
                                    <option>Another option</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Full Width Textarea</label>
                            <textarea class="form-textarea" placeholder="This textarea should not exceed container width"></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Layout testing script
        function updateTestInfo() {
            const screenWidth = window.screen.width;
            const screenHeight = window.screen.height;
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;
            
            document.getElementById('screenSize').textContent = `${screenWidth}x${screenHeight}`;
            document.getElementById('viewportSize').textContent = `${viewportWidth}x${viewportHeight}`;
            
            // Check for horizontal overflow
            const hasHorizontalOverflow = document.documentElement.scrollWidth > document.documentElement.clientWidth;
            const overflowStatus = document.getElementById('overflowStatus');
            
            if (hasHorizontalOverflow) {
                overflowStatus.textContent = 'OVERFLOW DETECTED!';
                overflowStatus.style.color = '#ff4444';
            } else {
                overflowStatus.textContent = 'No Overflow ✓';
                overflowStatus.style.color = '#44ff44';
            }
        }

        // Sidebar toggle functionality
        document.getElementById('sidebarToggle').addEventListener('click', function() {
            const sidebar = document.getElementById('adminSidebar');
            const main = document.getElementById('adminMain');
            
            sidebar.classList.toggle('collapsed');
            main.classList.toggle('expanded');
            
            setTimeout(updateTestInfo, 300); // Check after animation
        });

        // Update test info on load and resize
        window.addEventListener('load', updateTestInfo);
        window.addEventListener('resize', updateTestInfo);
        
        // Initial update
        updateTestInfo();
        
        // Periodic check for overflow
        setInterval(updateTestInfo, 1000);
    </script>
</body>
</html>
