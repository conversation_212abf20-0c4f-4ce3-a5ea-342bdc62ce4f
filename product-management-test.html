<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Management Test - VAITH</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/admin.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: var(--admin-bg);
            color: var(--admin-text-primary);
            font-family: 'Inter', sans-serif;
            padding: 2rem;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .test-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--admin-primary), var(--admin-primary-hover));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .test-description {
            color: var(--admin-text-secondary);
            font-size: 1.1rem;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .test-card {
            background: var(--admin-card-bg);
            border-radius: 12px;
            padding: 2rem;
            box-shadow: var(--admin-shadow-lg);
            border: 1px solid var(--admin-border);
        }
        
        .test-card-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .test-card-icon {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }
        
        .test-card-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0;
        }
        
        .test-card-description {
            color: var(--admin-text-secondary);
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }
        
        .test-actions {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }
        
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            margin-top: 1rem;
        }
        
        .status-success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--admin-success);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }
        
        .status-warning {
            background: rgba(245, 158, 11, 0.1);
            color: var(--admin-warning);
            border: 1px solid rgba(245, 158, 11, 0.2);
        }
        
        .status-info {
            background: rgba(59, 130, 246, 0.1);
            color: var(--admin-primary);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }
        
        .workflow-section {
            background: var(--admin-card-bg);
            border-radius: 12px;
            padding: 2rem;
            box-shadow: var(--admin-shadow-lg);
            border: 1px solid var(--admin-border);
        }
        
        .workflow-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .workflow-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }
        
        .workflow-step {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: var(--admin-bg);
            border-radius: 8px;
            border: 1px solid var(--admin-border);
        }
        
        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: var(--admin-primary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.875rem;
        }
        
        .step-content {
            flex: 1;
        }
        
        .step-title {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }
        
        .step-description {
            font-size: 0.875rem;
            color: var(--admin-text-secondary);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1 class="test-title">🧪 Product Management System Test</h1>
            <p class="test-description">
                Test the complete product management workflow from clearing existing data to adding new products and viewing them on the frontend.
            </p>
        </div>
        
        <div class="test-grid">
            <div class="test-card">
                <div class="test-card-header">
                    <div class="test-card-icon" style="background: linear-gradient(135deg, #ef4444, #dc2626);">
                        <i class="fas fa-trash-alt"></i>
                    </div>
                    <h3 class="test-card-title">1. Clear Existing Data</h3>
                </div>
                <p class="test-card-description">
                    Remove all existing product data to start with a clean slate. This ensures no old demo data interferes with testing.
                </p>
                <div class="test-actions">
                    <a href="clear-products.html" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Clear Product Data
                    </a>
                </div>
                <div class="status-indicator status-info" id="clearStatus">
                    <i class="fas fa-info-circle"></i>
                    <span id="clearStatusText">Ready to clear data</span>
                </div>
            </div>
            
            <div class="test-card">
                <div class="test-card-header">
                    <div class="test-card-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                        <i class="fas fa-plus"></i>
                    </div>
                    <h3 class="test-card-title">2. Add New Products</h3>
                </div>
                <p class="test-card-description">
                    Use the admin panel to add new products. Test the complete add product form with validation and data persistence.
                </p>
                <div class="test-actions">
                    <a href="admin-products.html" class="btn btn-primary">
                        <i class="fas fa-cog"></i> Admin Products Panel
                    </a>
                    <a href="admin-dashboard.html" class="btn btn-secondary">
                        <i class="fas fa-tachometer-alt"></i> Admin Dashboard
                    </a>
                </div>
                <div class="status-indicator status-info" id="addStatus">
                    <i class="fas fa-info-circle"></i>
                    <span id="addStatusText">Ready to add products</span>
                </div>
            </div>
            
            <div class="test-card">
                <div class="test-card-header">
                    <div class="test-card-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                        <i class="fas fa-eye"></i>
                    </div>
                    <h3 class="test-card-title">3. View on Frontend</h3>
                </div>
                <p class="test-card-description">
                    Check that newly added products appear correctly on the main website pages with proper formatting and functionality.
                </p>
                <div class="test-actions">
                    <a href="index.html" class="btn btn-primary">
                        <i class="fas fa-home"></i> Homepage
                    </a>
                    <a href="products.html" class="btn btn-secondary">
                        <i class="fas fa-box"></i> Products Page
                    </a>
                </div>
                <div class="status-indicator status-info" id="viewStatus">
                    <i class="fas fa-info-circle"></i>
                    <span id="viewStatusText">Ready to view products</span>
                </div>
            </div>
        </div>
        
        <div class="workflow-section">
            <h2 class="workflow-title">
                <i class="fas fa-route"></i>
                Complete Testing Workflow
            </h2>
            <div class="workflow-steps">
                <div class="workflow-step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <div class="step-title">Clear Data</div>
                        <div class="step-description">Remove existing products</div>
                    </div>
                </div>
                <div class="workflow-step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <div class="step-title">Login as Admin</div>
                        <div class="step-description">Access admin panel</div>
                    </div>
                </div>
                <div class="workflow-step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <div class="step-title">Add Products</div>
                        <div class="step-description">Use the add product form</div>
                    </div>
                </div>
                <div class="workflow-step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <div class="step-title">Verify Admin</div>
                        <div class="step-description">Check admin dashboard</div>
                    </div>
                </div>
                <div class="workflow-step">
                    <div class="step-number">5</div>
                    <div class="step-content">
                        <div class="step-title">Test Frontend</div>
                        <div class="step-description">View on main website</div>
                    </div>
                </div>
                <div class="workflow-step">
                    <div class="step-number">6</div>
                    <div class="step-content">
                        <div class="step-title">Verify Features</div>
                        <div class="step-description">Test cart, favorites, etc.</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/admin.js"></script>
    <script>
        // Check current system status
        document.addEventListener('DOMContentLoaded', function() {
            updateSystemStatus();
        });
        
        function updateSystemStatus() {
            // Check product count
            const products = localStorage.getItem('vaith_products');
            const productCount = products ? JSON.parse(products).length : 0;
            
            // Update clear status
            const clearStatus = document.getElementById('clearStatus');
            const clearStatusText = document.getElementById('clearStatusText');
            
            if (productCount === 0) {
                clearStatus.className = 'status-indicator status-success';
                clearStatusText.innerHTML = '<i class="fas fa-check"></i> System is clean (0 products)';
            } else {
                clearStatus.className = 'status-indicator status-warning';
                clearStatusText.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${productCount} product(s) found`;
            }
            
            // Update add status
            const addStatus = document.getElementById('addStatus');
            const addStatusText = document.getElementById('addStatusText');
            
            if (productCount === 0) {
                addStatus.className = 'status-indicator status-info';
                addStatusText.innerHTML = '<i class="fas fa-plus"></i> Ready to add first product';
            } else {
                addStatus.className = 'status-indicator status-success';
                addStatusText.innerHTML = `<i class="fas fa-check"></i> ${productCount} product(s) in system`;
            }
            
            // Update view status
            const viewStatus = document.getElementById('viewStatus');
            const viewStatusText = document.getElementById('viewStatusText');
            
            if (productCount === 0) {
                viewStatus.className = 'status-indicator status-warning';
                viewStatusText.innerHTML = '<i class="fas fa-exclamation-triangle"></i> No products to display';
            } else {
                viewStatus.className = 'status-indicator status-success';
                viewStatusText.innerHTML = `<i class="fas fa-check"></i> ${productCount} product(s) ready to view`;
            }
        }
        
        // Refresh status every 5 seconds
        setInterval(updateSystemStatus, 5000);
    </script>
</body>
</html>
