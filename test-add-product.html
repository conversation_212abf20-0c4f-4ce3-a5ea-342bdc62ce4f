<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Add Product - VAITH</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/admin.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: var(--admin-bg);
            color: var(--admin-text-primary);
            font-family: 'Inter', sans-serif;
            padding: 2rem;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: var(--admin-card-bg);
            border-radius: 12px;
            padding: 2rem;
            box-shadow: var(--admin-shadow-lg);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .test-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        
        .test-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-bottom: 2rem;
        }
        
        .status-log {
            background: var(--admin-bg);
            border: 1px solid var(--admin-border);
            border-radius: 8px;
            padding: 1rem;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
        }
        
        .product-form {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group.full-width {
            grid-column: 1 / -1;
        }
        
        label {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--admin-text-primary);
        }
        
        input, select, textarea {
            padding: 0.75rem;
            border: 1px solid var(--admin-border);
            border-radius: 6px;
            background: var(--admin-card-bg);
            color: var(--admin-text-primary);
            font-size: 0.875rem;
        }
        
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: var(--admin-primary);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: var(--admin-primary);
            color: white;
        }
        
        .btn-primary:hover {
            background: var(--admin-primary-hover);
        }
        
        .btn-secondary {
            background: var(--admin-border);
            color: var(--admin-text-primary);
        }
        
        .btn-secondary:hover {
            background: var(--admin-border-strong);
        }
        
        .btn-success {
            background: var(--admin-success);
            color: white;
        }
        
        .btn-danger {
            background: var(--admin-danger);
            color: white;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1 class="test-title">🧪 Add Product Functionality Test</h1>
            <p>Test the add product functionality independently</p>
        </div>
        
        <div class="test-actions">
            <button class="btn btn-primary" onclick="testAddProduct()">
                <i class="fas fa-plus"></i> Test Add Product
            </button>
            <button class="btn btn-secondary" onclick="checkProducts()">
                <i class="fas fa-list"></i> Check Products
            </button>
            <button class="btn btn-danger" onclick="clearProducts()">
                <i class="fas fa-trash"></i> Clear Products
            </button>
            <button class="btn btn-success" onclick="goToAdmin()">
                <i class="fas fa-cog"></i> Go to Admin
            </button>
        </div>
        
        <form id="testProductForm" class="product-form">
            <div class="form-group">
                <label for="testName">Product Name *</label>
                <input type="text" id="testName" name="name" required placeholder="Test Product" value="Test Product">
            </div>
            
            <div class="form-group">
                <label for="testCategory">Category *</label>
                <select id="testCategory" name="category" required>
                    <option value="women">Women</option>
                    <option value="men">Men</option>
                    <option value="accessories">Accessories</option>
                    <option value="shoes">Shoes</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="testPrice">Price *</label>
                <input type="number" id="testPrice" name="price" step="0.01" required placeholder="29.99" value="29.99">
            </div>
            
            <div class="form-group">
                <label for="testStock">Stock *</label>
                <input type="number" id="testStock" name="stock" required placeholder="10" value="10">
            </div>
            
            <div class="form-group full-width">
                <label for="testDescription">Description</label>
                <textarea id="testDescription" name="description" rows="3" placeholder="Test product description">This is a test product for testing the add product functionality.</textarea>
            </div>
            
            <div class="form-group full-width">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Add Test Product
                </button>
            </div>
        </form>
        
        <div class="status-log" id="statusLog">
Ready to test add product functionality...
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/auth.js"></script>
    <script src="js/admin.js"></script>
    <script src="js/admin-common.js"></script>

    <script>
        let adminManager;
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('DOM loaded, initializing...');
            
            // Initialize admin manager
            if (typeof AdminManager !== 'undefined') {
                adminManager = new AdminManager();
                log('✅ AdminManager initialized successfully');
            } else {
                log('❌ AdminManager class not found');
            }
            
            // Setup form submission
            document.getElementById('testProductForm').addEventListener('submit', function(e) {
                e.preventDefault();
                addTestProduct();
            });
            
            log('🚀 Test page ready');
        });
        
        function log(message) {
            const logElement = document.getElementById('statusLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `\n[${timestamp}] ${message}`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function testAddProduct() {
            log('🧪 Testing add product functionality...');
            
            if (!adminManager) {
                log('❌ AdminManager not available');
                return;
            }
            
            const testProduct = {
                name: 'Test Product ' + Date.now(),
                category: 'women',
                price: 29.99,
                stock: 10,
                description: 'This is a test product',
                brand: 'VAITH',
                sku: 'TEST-' + Date.now(),
                status: 'active'
            };
            
            try {
                const result = adminManager.addProduct(testProduct);
                log('✅ Product added successfully: ' + result.name);
                log('📊 Product ID: ' + result.id);
                checkProducts();
            } catch (error) {
                log('❌ Error adding product: ' + error.message);
            }
        }
        
        function addTestProduct() {
            log('📝 Adding product from form...');
            
            if (!adminManager) {
                log('❌ AdminManager not available');
                return;
            }
            
            const formData = new FormData(document.getElementById('testProductForm'));
            const productData = {};
            
            for (let [key, value] of formData.entries()) {
                productData[key] = value;
            }
            
            // Convert numeric fields
            productData.price = parseFloat(productData.price);
            productData.stock = parseInt(productData.stock);
            productData.brand = 'VAITH';
            productData.sku = 'FORM-' + Date.now();
            productData.status = 'active';
            
            try {
                const result = adminManager.addProduct(productData);
                log('✅ Form product added: ' + result.name);
                log('📊 Product ID: ' + result.id);
                checkProducts();
            } catch (error) {
                log('❌ Error adding form product: ' + error.message);
            }
        }
        
        function checkProducts() {
            log('📋 Checking current products...');
            
            if (!adminManager) {
                log('❌ AdminManager not available');
                return;
            }
            
            const products = adminManager.getAllProducts();
            log(`📦 Total products: ${products.length}`);
            
            if (products.length > 0) {
                products.forEach((product, index) => {
                    log(`  ${index + 1}. ${product.name} (${product.category}) - $${product.price}`);
                });
            } else {
                log('📭 No products found');
            }
        }
        
        function clearProducts() {
            log('🗑️ Clearing all products...');
            localStorage.removeItem('vaith_products');
            
            if (adminManager) {
                adminManager.products = [];
                adminManager.saveProducts();
            }
            
            log('✅ Products cleared');
            checkProducts();
        }
        
        function goToAdmin() {
            log('🔗 Redirecting to admin products page...');
            window.location.href = 'admin-products.html';
        }
    </script>
</body>
</html>
