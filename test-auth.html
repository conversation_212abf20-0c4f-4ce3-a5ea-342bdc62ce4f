<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auth Test - VAITH</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-form {
            display: flex;
            gap: 10px;
            margin: 10px 0;
        }
        input, button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #4B0082;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #6a1b9a;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            background: #e8f5e8;
            border: 1px solid #4caf50;
        }
        .error {
            background: #ffeaea;
            border-color: #f44336;
            color: #d32f2f;
        }
        .info {
            background: #e3f2fd;
            border-color: #2196f3;
            color: #1976d2;
        }
    </style>
</head>
<body>
    <h1>VAITH Authentication System Test</h1>
    
    <div class="test-section">
        <h2>Current Status</h2>
        <div id="currentStatus" class="status info">Loading...</div>
    </div>

    <div class="test-section">
        <h2>Test Login</h2>
        <div class="test-form">
            <input type="email" id="loginEmail" placeholder="Email" value="<EMAIL>">
            <input type="password" id="loginPassword" placeholder="Password" value="admin123">
            <button onclick="testLogin()">Login</button>
        </div>
        <div class="test-form">
            <input type="email" id="loginEmail2" placeholder="Email" value="<EMAIL>">
            <input type="password" id="loginPassword2" placeholder="Password" value="user123">
            <button onclick="testLogin2()">Login as User</button>
        </div>
    </div>

    <div class="test-section">
        <h2>Test Signup</h2>
        <div class="test-form">
            <input type="text" id="signupFirstName" placeholder="First Name" value="Test">
            <input type="text" id="signupLastName" placeholder="Last Name" value="User">
        </div>
        <div class="test-form">
            <input type="email" id="signupEmail" placeholder="Email" value="<EMAIL>">
            <input type="password" id="signupPassword" placeholder="Password" value="testpass123">
        </div>
        <div class="test-form">
            <button onclick="testSignup()">Sign Up</button>
        </div>
    </div>

    <div class="test-section">
        <h2>Actions</h2>
        <div class="test-form">
            <button onclick="testLogout()">Logout</button>
            <button onclick="clearStorage()">Clear Storage</button>
            <button onclick="updateStatus()">Refresh Status</button>
        </div>
    </div>

    <div class="test-section">
        <h2>Navigation Links</h2>
        <div class="test-form">
            <a href="index.html" style="margin-right: 10px;">Home</a>
            <a href="login.html" style="margin-right: 10px;">Login</a>
            <a href="signup.html" style="margin-right: 10px;">Signup</a>
            <a href="user-profile.html" style="margin-right: 10px;">Profile</a>
            <a href="admin-dashboard.html">Admin</a>
        </div>
    </div>

    <script src="js/auth.js"></script>
    <script>
        function updateStatus() {
            const statusDiv = document.getElementById('currentStatus');
            
            if (authManager.isLoggedIn()) {
                const user = authManager.getCurrentUser();
                statusDiv.innerHTML = `
                    <strong>Logged in as:</strong> ${user.firstName} ${user.lastName} (${user.email})<br>
                    <strong>Role:</strong> ${user.role}<br>
                    <strong>Status:</strong> ${user.status}<br>
                    <strong>Join Date:</strong> ${new Date(user.joinDate).toLocaleDateString()}
                `;
                statusDiv.className = 'status';
            } else {
                statusDiv.innerHTML = '<strong>Not logged in</strong>';
                statusDiv.className = 'status info';
            }
        }

        async function testLogin() {
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            
            try {
                const user = await authManager.login(email, password);
                alert(`Login successful! Welcome ${user.firstName}`);
                updateStatus();
            } catch (error) {
                alert(`Login failed: ${error.message}`);
            }
        }

        async function testLogin2() {
            const email = document.getElementById('loginEmail2').value;
            const password = document.getElementById('loginPassword2').value;
            
            try {
                const user = await authManager.login(email, password);
                alert(`Login successful! Welcome ${user.firstName}`);
                updateStatus();
            } catch (error) {
                alert(`Login failed: ${error.message}`);
            }
        }

        async function testSignup() {
            const firstName = document.getElementById('signupFirstName').value;
            const lastName = document.getElementById('signupLastName').value;
            const email = document.getElementById('signupEmail').value;
            const password = document.getElementById('signupPassword').value;
            
            try {
                const user = await authManager.signup({
                    firstName,
                    lastName,
                    email,
                    password
                });
                alert(`Signup successful! Welcome ${user.firstName}`);
                updateStatus();
            } catch (error) {
                alert(`Signup failed: ${error.message}`);
            }
        }

        function testLogout() {
            if (authManager.isLoggedIn()) {
                if (confirm('Are you sure you want to logout?')) {
                    authManager.logout();
                }
            } else {
                alert('Not logged in');
            }
        }

        function clearStorage() {
            localStorage.clear();
            alert('Storage cleared');
            updateStatus();
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus();
        });
    </script>
</body>
</html>
