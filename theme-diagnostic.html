<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Theme Toggle Diagnostic - VAITH</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .diagnostic-container {
            max-width: 1000px;
            margin: 2rem auto;
            padding: 2rem;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
        }
        
        .diagnostic-section {
            margin: 2rem 0;
            padding: 1rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: var(--section-bg);
        }
        
        .diagnostic-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .diagnostic-item {
            padding: 1rem;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.9rem;
        }
        
        .status-good { color: var(--success-color); }
        .status-warning { color: var(--warning-color); }
        .status-error { color: var(--error-color); }
        
        .test-button {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            margin: 0.25rem;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            background: #6a1b9a;
        }
        
        .console-output {
            background: var(--input-bg);
            border: 1px solid var(--input-border);
            padding: 1rem;
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.8rem;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            color: var(--text-color);
        }
    </style>
</head>
<body>
    <!-- Simple Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-left">
                <div class="logo">
                    <h2>VAITH</h2>
                    <span>Diagnostic</span>
                </div>
            </div>
            <div class="nav-right">
                <div class="nav-icons">
                    <button class="nav-icon theme-toggle" id="themeToggle" title="Toggle dark mode">
                        <i class="fas fa-moon" id="themeIcon"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <div class="diagnostic-container">
        <h1>Theme Toggle Diagnostic Tool</h1>
        <p>This tool helps diagnose theme toggle issues and provides detailed information about the current state.</p>

        <div class="diagnostic-section">
            <h2>Quick Actions</h2>
            <button class="test-button" onclick="runDiagnostic()">Run Full Diagnostic</button>
            <button class="test-button" onclick="testToggle()">Test Toggle</button>
            <button class="test-button" onclick="clearConsole()">Clear Console</button>
            <button class="test-button" onclick="exportDiagnostic()">Export Results</button>
        </div>

        <div class="diagnostic-section">
            <h2>Current Status</h2>
            <div class="diagnostic-grid" id="statusGrid">
                <!-- Status items will be populated by JavaScript -->
            </div>
        </div>

        <div class="diagnostic-section">
            <h2>CSS Variables Test</h2>
            <div class="diagnostic-grid" id="cssVariablesGrid">
                <!-- CSS variables will be populated by JavaScript -->
            </div>
        </div>

        <div class="diagnostic-section">
            <h2>Element Detection</h2>
            <div class="diagnostic-grid" id="elementsGrid">
                <!-- Elements will be populated by JavaScript -->
            </div>
        </div>

        <div class="diagnostic-section">
            <h2>Console Output</h2>
            <div class="console-output" id="consoleOutput">
                Diagnostic console initialized...
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/theme-toggle.js"></script>
    
    <script>
        // Diagnostic functions
        let consoleLog = [];
        
        // Override console.log to capture output
        const originalConsoleLog = console.log;
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            consoleLog.push(args.join(' '));
            updateConsoleOutput();
        };

        function updateConsoleOutput() {
            const output = document.getElementById('consoleOutput');
            output.textContent = consoleLog.slice(-50).join('\n'); // Show last 50 lines
            output.scrollTop = output.scrollHeight;
        }

        function clearConsole() {
            consoleLog = [];
            updateConsoleOutput();
        }

        function runDiagnostic() {
            console.log('🔍 Running full diagnostic...');
            
            updateStatusGrid();
            updateCSSVariablesGrid();
            updateElementsGrid();
            
            console.log('✅ Diagnostic complete');
        }

        function updateStatusGrid() {
            const grid = document.getElementById('statusGrid');
            const currentTheme = document.documentElement.getAttribute('data-theme') || 'none';
            const bodyClasses = document.body.className || 'none';
            const localStorageTheme = localStorage.getItem('theme') || 'none';
            const initialized = window.themeToggleState?.initialized || false;
            const utilsAvailable = !!window.themeToggleUtils;
            
            grid.innerHTML = `
                <div class="diagnostic-item">
                    <strong>Current Theme:</strong><br>
                    <span class="${currentTheme !== 'none' ? 'status-good' : 'status-error'}">${currentTheme}</span>
                </div>
                <div class="diagnostic-item">
                    <strong>Body Classes:</strong><br>
                    <span class="${bodyClasses.includes('dark-theme') || bodyClasses === 'none' ? 'status-good' : 'status-warning'}">${bodyClasses}</span>
                </div>
                <div class="diagnostic-item">
                    <strong>LocalStorage:</strong><br>
                    <span class="${localStorageTheme !== 'none' ? 'status-good' : 'status-warning'}">${localStorageTheme}</span>
                </div>
                <div class="diagnostic-item">
                    <strong>Initialized:</strong><br>
                    <span class="${initialized ? 'status-good' : 'status-error'}">${initialized}</span>
                </div>
                <div class="diagnostic-item">
                    <strong>Utils Available:</strong><br>
                    <span class="${utilsAvailable ? 'status-good' : 'status-error'}">${utilsAvailable}</span>
                </div>
                <div class="diagnostic-item">
                    <strong>System Preference:</strong><br>
                    <span class="status-good">${window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'}</span>
                </div>
            `;
        }

        function updateCSSVariablesGrid() {
            const grid = document.getElementById('cssVariablesGrid');
            const style = getComputedStyle(document.documentElement);
            
            const variables = [
                '--background-color',
                '--text-color',
                '--card-bg',
                '--border-color',
                '--modal-bg',
                '--modal-overlay',
                '--primary-color'
            ];
            
            let html = '';
            variables.forEach(varName => {
                const value = style.getPropertyValue(varName).trim();
                const status = value ? 'status-good' : 'status-error';
                html += `
                    <div class="diagnostic-item">
                        <strong>${varName}:</strong><br>
                        <span class="${status}">${value || 'Not set'}</span>
                    </div>
                `;
            });
            
            grid.innerHTML = html;
        }

        function updateElementsGrid() {
            const grid = document.getElementById('elementsGrid');
            
            const elements = [
                { name: 'Theme Toggle Button', selector: '#themeToggle' },
                { name: 'Theme Icon', selector: '#themeIcon' },
                { name: 'Theme Toggle (class)', selector: '.theme-toggle' },
                { name: 'Navbar', selector: '.navbar' },
                { name: 'Body', selector: 'body' },
                { name: 'HTML', selector: 'html' }
            ];
            
            let html = '';
            elements.forEach(element => {
                const found = document.querySelector(element.selector);
                const status = found ? 'status-good' : 'status-error';
                html += `
                    <div class="diagnostic-item">
                        <strong>${element.name}:</strong><br>
                        <span class="${status}">${found ? 'Found' : 'Not found'}</span><br>
                        <small>${element.selector}</small>
                    </div>
                `;
            });
            
            grid.innerHTML = html;
        }

        function testToggle() {
            console.log('🧪 Testing theme toggle...');
            if (window.themeToggleUtils) {
                window.themeToggleUtils.testToggle();
            } else {
                console.log('❌ Theme toggle utils not available');
            }
            setTimeout(runDiagnostic, 500);
        }

        function exportDiagnostic() {
            const data = {
                timestamp: new Date().toISOString(),
                theme: document.documentElement.getAttribute('data-theme'),
                bodyClasses: document.body.className,
                localStorage: localStorage.getItem('theme'),
                initialized: window.themeToggleState?.initialized,
                utilsAvailable: !!window.themeToggleUtils,
                consoleLog: consoleLog.slice(-20)
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'theme-diagnostic-' + Date.now() + '.json';
            a.click();
            URL.revokeObjectURL(url);
            
            console.log('📄 Diagnostic exported');
        }

        // Initialize diagnostic
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Theme diagnostic tool loaded');
            setTimeout(runDiagnostic, 1000);
            
            // Auto-refresh every 5 seconds
            setInterval(runDiagnostic, 5000);
        });

        // Listen for theme changes
        document.addEventListener('themeChanged', function(e) {
            console.log('🎨 Theme change detected:', e.detail);
            setTimeout(runDiagnostic, 100);
        });
    </script>
</body>
</html>
