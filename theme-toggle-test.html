<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Theme Toggle Test - VAITH</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .test-section {
            margin: 2rem 0;
            padding: 2rem;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .test-card {
            padding: 1rem;
            background: var(--section-bg);
            border: 1px solid var(--border-color);
            border-radius: 4px;
        }
        
        .status-display {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: var(--card-bg);
            border: 2px solid var(--border-color);
            padding: 1rem;
            border-radius: 8px;
            font-family: monospace;
            font-size: 0.9rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }
        
        .test-button {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            margin: 0.25rem;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            background: #6a1b9a;
            transform: translateY(-1px);
        }
        
        .test-input {
            background: var(--input-bg);
            color: var(--text-color);
            border: 1px solid var(--input-border);
            padding: 0.5rem;
            border-radius: 4px;
            margin: 0.25rem;
        }
        
        .test-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--modal-overlay);
            z-index: 2000;
        }
        
        .test-modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: var(--modal-bg);
            padding: 2rem;
            border-radius: 8px;
            border: 1px solid var(--border-color);
            max-width: 500px;
            width: 90%;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-left">
                <div class="logo">
                    <h2>VAITH</h2>
                    <span>Theme Test</span>
                </div>
            </div>

            <div class="nav-right">
                <div class="nav-icons">
                    <button class="nav-icon theme-toggle" id="themeToggle" title="Toggle dark mode" aria-label="Toggle dark mode">
                        <i class="fas fa-moon" id="themeIcon" aria-hidden="true"></i>
                    </button>
                    <button class="nav-icon">
                        <i class="fas fa-user"></i>
                    </button>
                    <button class="nav-icon">
                        <i class="fas fa-heart"></i>
                        <span class="badge">3</span>
                    </button>
                    <button class="nav-icon" onclick="showTestModal()">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="badge">2</span>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <div class="test-container">
        <h1>Theme Toggle Comprehensive Test</h1>
        <p>This page tests all theme toggle functionality across different UI components.</p>

        <div class="test-section">
            <h2>Theme Controls</h2>
            <button class="test-button" onclick="window.themeToggleUtils.testToggle()">Manual Toggle Test</button>
            <button class="test-button" onclick="window.themeToggleUtils.setTheme('light')">Force Light</button>
            <button class="test-button" onclick="window.themeToggleUtils.setTheme('dark')">Force Dark</button>
            <button class="test-button" onclick="window.themeToggleUtils.reinitialize()">Reinitialize</button>
            <button class="test-button" onclick="updateStatus()">Update Status</button>
        </div>

        <div class="test-section">
            <h2>UI Components Test</h2>
            <div class="test-grid">
                <div class="test-card">
                    <h3>Form Elements</h3>
                    <input type="text" class="test-input" placeholder="Test input field">
                    <button class="test-button">Test Button</button>
                </div>
                <div class="test-card">
                    <h3>Text Colors</h3>
                    <p style="color: var(--text-color)">Primary text color</p>
                    <p style="color: var(--text-light)">Light text color</p>
                    <a href="#" style="color: var(--link-color, var(--primary-color))">Link color</a>
                </div>
                <div class="test-card">
                    <h3>Background Colors</h3>
                    <div style="background: var(--section-bg); padding: 1rem; margin: 0.5rem 0;">Section background</div>
                    <div style="background: var(--card-bg); padding: 1rem; margin: 0.5rem 0; border: 1px solid var(--border-color);">Card background</div>
                </div>
                <div class="test-card">
                    <h3>Status Colors</h3>
                    <div style="color: var(--success-color);">✓ Success color</div>
                    <div style="color: var(--warning-color);">⚠ Warning color</div>
                    <div style="color: var(--error-color);">✗ Error color</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>Navigation Test</h2>
            <p>The theme toggle button in the navigation should work properly and show the correct icon.</p>
            <ul>
                <li>Light mode: Shows moon icon 🌙</li>
                <li>Dark mode: Shows sun icon ☀️</li>
                <li>Hover effects should work in both themes</li>
                <li>Click should toggle between themes smoothly</li>
            </ul>
        </div>
    </div>

    <!-- Test Modal -->
    <div class="test-modal" id="testModal">
        <div class="test-modal-content">
            <h3>Test Modal</h3>
            <p>This modal should display correctly in both light and dark themes.</p>
            <p>Background: <span style="background: var(--modal-bg); padding: 0.25rem;">Modal background</span></p>
            <p>Overlay should be darker in dark mode.</p>
            <button class="test-button" onclick="hideTestModal()">Close Modal</button>
        </div>
    </div>

    <!-- Status Display -->
    <div class="status-display" id="statusDisplay">
        <div><strong>Theme Status:</strong></div>
        <div>Current: <span id="currentTheme">loading...</span></div>
        <div>Data-theme: <span id="dataTheme">loading...</span></div>
        <div>Body class: <span id="bodyClass">loading...</span></div>
        <div>Initialized: <span id="initialized">loading...</span></div>
        <div>LocalStorage: <span id="localStorage">loading...</span></div>
    </div>

    <!-- Scripts -->
    <script src="js/theme-toggle.js"></script>
    
    <script>
        // Update status display
        function updateStatus() {
            const currentTheme = window.themeToggleUtils?.getCurrentTheme() || 'unknown';
            const dataTheme = document.documentElement.getAttribute('data-theme') || 'none';
            const bodyClasses = document.body.className || 'none';
            const initialized = window.themeToggleUtils?.isInitialized() || false;
            const localStorageTheme = localStorage.getItem('theme') || 'none';
            
            document.getElementById('currentTheme').textContent = currentTheme;
            document.getElementById('dataTheme').textContent = dataTheme;
            document.getElementById('bodyClass').textContent = bodyClasses;
            document.getElementById('initialized').textContent = initialized;
            document.getElementById('localStorage').textContent = localStorageTheme;
        }

        // Modal functions
        function showTestModal() {
            document.getElementById('testModal').style.display = 'block';
        }

        function hideTestModal() {
            document.getElementById('testModal').style.display = 'none';
        }

        // Initialize status display
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(updateStatus, 500);
            
            // Update status when theme changes
            document.addEventListener('themeChanged', function(e) {
                console.log('Theme changed event received:', e.detail);
                setTimeout(updateStatus, 100);
            });
        });

        // Auto-update status every 2 seconds
        setInterval(updateStatus, 2000);

        console.log('🧪 Theme Toggle Test Page Loaded');
    </script>
</body>
</html>
